import { useState, useEffect } from 'react';

const DomainSelector = ({ projects, selectedProjects, onProjectsSelect, loading }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDomain, setSelectedDomain] = useState('');
  const [groupedProjects, setGroupedProjects] = useState({});

  // Group projects by domain
  useEffect(() => {
    const grouped = {};
    projects.forEach(project => {
      const domain = project.url;
      if (!grouped[domain]) {
        grouped[domain] = [];
      }
      grouped[domain].push(project);
    });
    
    // Sort domains by number of projects (descending)
    const sortedGrouped = Object.keys(grouped)
      .sort((a, b) => grouped[b].length - grouped[a].length)
      .reduce((acc, domain) => {
        acc[domain] = grouped[domain].sort((a, b) => a.name.localeCompare(b.name));
        return acc;
      }, {});
    
    setGroupedProjects(sortedGrouped);
  }, [projects]);

  // Filter domains based on search term
  const filteredDomains = Object.keys(groupedProjects).filter(domain =>
    domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
    groupedProjects[domain].some(project => 
      project.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const handleDomainChange = (e) => {
    const domain = e.target.value;
    setSelectedDomain(domain);
    
    if (domain) {
      // Select all projects for this domain
      onProjectsSelect(groupedProjects[domain]);
    } else {
      onProjectsSelect([]);
    }
  };

  const handleProjectToggle = (project) => {
    const isSelected = selectedProjects.some(p => p.id === project.id);
    
    if (isSelected) {
      // Remove project
      onProjectsSelect(selectedProjects.filter(p => p.id !== project.id));
    } else {
      // Add project
      onProjectsSelect([...selectedProjects, project]);
    }
  };

  const handleSelectAllForDomain = (domain) => {
    const domainProjects = groupedProjects[domain];
    const allSelected = domainProjects.every(project => 
      selectedProjects.some(p => p.id === project.id)
    );
    
    if (allSelected) {
      // Deselect all projects from this domain
      onProjectsSelect(selectedProjects.filter(p => 
        !domainProjects.some(dp => dp.id === p.id)
      ));
    } else {
      // Select all projects from this domain
      const newProjects = domainProjects.filter(project => 
        !selectedProjects.some(p => p.id === project.id)
      );
      onProjectsSelect([...selectedProjects, ...newProjects]);
    }
  };

  const clearAllSelections = () => {
    onProjectsSelect([]);
    setSelectedDomain('');
  };

  return (
    <div className="domain-selector">
      <h3>Select Projects by Domain</h3>
      <p className="selector-description">
        🌐 View rankings for multiple projects from the same domain on one map
      </p>
      
      {loading ? (
        <div className="loading">Loading projects...</div>
      ) : (
        <>
          <div className="search-box">
            <input
              type="text"
              placeholder="Search domains or projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="domain-select-container">
            <select
              value={selectedDomain}
              onChange={handleDomainChange}
              className="domain-select"
            >
              <option value="">Choose a domain...</option>
              {filteredDomains.map(domain => (
                <option key={domain} value={domain}>
                  {domain} ({groupedProjects[domain].length} projects)
                </option>
              ))}
            </select>
            
            {selectedProjects.length > 0 && (
              <button
                onClick={clearAllSelections}
                className="clear-button"
                title="Clear all selections"
              >
                Clear All
              </button>
            )}
          </div>
          
          {selectedDomain && (
            <div className="domain-projects">
              <div className="domain-header">
                <h4>{selectedDomain}</h4>
                <button
                  onClick={() => handleSelectAllForDomain(selectedDomain)}
                  className="select-all-button"
                >
                  {groupedProjects[selectedDomain].every(project => 
                    selectedProjects.some(p => p.id === project.id)
                  ) ? 'Deselect All' : 'Select All'}
                </button>
              </div>
              
              <div className="project-list">
                {groupedProjects[selectedDomain].map(project => {
                  const isSelected = selectedProjects.some(p => p.id === project.id);
                  return (
                    <label key={project.id} className="project-checkbox">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleProjectToggle(project)}
                      />
                      <span className="project-name">{project.name}</span>
                      <span className="project-keywords">
                        {project.number_of_keywords} keywords
                      </span>
                    </label>
                  );
                })}
              </div>
            </div>
          )}
          
          {selectedProjects.length > 0 && (
            <div className="selected-summary">
              <h4>Selected Projects ({selectedProjects.length})</h4>
              <div className="selected-list">
                {selectedProjects.map(project => (
                  <div key={project.id} className="selected-item">
                    <span className="selected-name">{project.name}</span>
                    <button
                      onClick={() => handleProjectToggle(project)}
                      className="remove-button"
                      title="Remove project"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="summary-stats">
                <p>
                  <strong>Total Keywords:</strong> {' '}
                  {selectedProjects.reduce((sum, p) => sum + p.number_of_keywords, 0)}
                </p>
                <p>
                  <strong>Domains:</strong> {' '}
                  {new Set(selectedProjects.map(p => p.url)).size}
                </p>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default DomainSelector;
