# SerpRobot Rankings Map

A React-based web application that visualizes SerpRobot keyword rankings on an interactive map. This tool allows you to select projects, view their rankings geographically, and cycle through historical data to see ranking changes over time.

## Features

- **Dual View Modes**: Switch between single project view and multi-project domain view
- **Project Selection**: Browse and search through all available SerpRobot projects
- **Domain-Based Multi-Project View**: Select multiple projects from the same domain to compare rankings across locations
- **Interactive Map**: View project locations on a Leaflet-powered map with color-coded ranking markers
- **Keyword Filtering**: Filter rankings by specific keywords across single or multiple projects
- **Date Navigation**: Cycle through historical ranking data with playback controls
- **Ranking Visualization**: See rank numbers directly on map pins with color-coded performance indicators
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd serpmaps
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Usage

### Single Project Mode

1. **Select Single Project Mode**: Click the "📍 Single Project" button in the view mode toggle.

2. **Select a Project**: Use the dropdown to choose from available SerpRobot projects. You can search by project name or URL.

3. **View on Map**: If the project includes location information in its name, it will be displayed on the map with a color-coded marker showing the ranking.

### Multi-Project Mode

1. **Select Multi-Project Mode**: Click the "🌐 Multiple Projects" button in the view mode toggle.

2. **Choose a Domain**: Select a domain from the dropdown to see all projects for that domain.

3. **Select Projects**: Check the boxes for the projects you want to compare on the map.

4. **View Multiple Rankings**: See all selected projects displayed simultaneously on the map with their respective rankings.

### Common Features

5. **Filter Keywords**: Choose specific keywords to focus on, or view all keywords for the selected project(s).

6. **Navigate Dates**: Use the date controls to cycle through historical data. You can manually select dates or use the playback controls to automatically cycle through them from past to present.

7. **View Rankings**: Click on map markers to see detailed ranking information in a popup, including rank numbers, keyword details, and performance indicators.

## Technical Details

### Built With

- **React** - Frontend framework
- **Vite** - Build tool and development server
- **Leaflet** - Interactive maps
- **React-Leaflet** - React components for Leaflet
- **Axios** - HTTP client for API requests

### API Integration

The application integrates with the SerpRobot API to fetch:
- Project listings
- Project details and keywords
- Historical ranking data

### Location Detection

The app automatically extracts location information from project names using pattern matching for common city names and formats.

## Project Structure

```
src/
├── components/          # React components
│   ├── ProjectSelector.jsx
│   ├── KeywordFilter.jsx
│   └── DateControls.jsx
├── services/           # API service functions
│   └── serprobotApi.js
├── utils/              # Utility functions
│   └── locationExtractor.js
├── App.jsx             # Main application component
├── App.css             # Application styles
├── index.css           # Global styles
└── main.jsx            # Application entry point
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
