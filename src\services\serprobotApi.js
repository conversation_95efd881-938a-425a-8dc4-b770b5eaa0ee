import axios from 'axios';

const API_BASE_URL = 'https://api.serprobot.com/v1/api.php';
const API_KEY = '63d7be40bb4e41b7c57c75c67a01dfdd';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  params: {
    api_key: API_KEY
  }
});

/**
 * Get list of all projects
 */
export const getProjects = async () => {
  try {
    const response = await api.get('', {
      params: {
        action: 'list_projects'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching projects:', error);
    throw error;
  }
};

/**
 * Get project details including keywords
 */
export const getProject = async (projectId) => {
  try {
    const response = await api.get('', {
      params: {
        action: 'project',
        project_id: projectId
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching project:', error);
    throw error;
  }
};

/**
 * Get keyword details including historical data
 */
export const getKeyword = async (keywordId) => {
  try {
    const response = await api.get('', {
      params: {
        action: 'keyword',
        keyword_id: keywordId
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching keyword:', error);
    throw error;
  }
};

/**
 * Get all keywords data for a project with full historical data
 */
export const getProjectKeywords = async (projectId) => {
  try {
    const project = await getProject(projectId);
    const keywordsData = await Promise.all(
      project.keywords.map(keyword => getKeyword(keyword.id))
    );
    return keywordsData;
  } catch (error) {
    console.error('Error fetching project keywords:', error);
    throw error;
  }
};

/**
 * Get project with enhanced keyword data including historical information
 */
export const getProjectWithFullData = async (projectId) => {
  try {
    const project = await getProject(projectId);

    // Get full keyword data for the first few keywords to get historical dates
    // We'll limit this to avoid too many API calls
    const keywordPromises = project.keywords.slice(0, 3).map(keyword =>
      getKeyword(keyword.id)
    );

    const fullKeywordData = await Promise.all(keywordPromises);

    // Extract all unique dates from historical data
    const allDates = new Set();
    fullKeywordData.forEach(keywordData => {
      if (keywordData.check_data && Array.isArray(keywordData.check_data)) {
        keywordData.check_data.forEach(check => {
          if (check.created) {
            allDates.add(check.created.split(' ')[0]); // Get date part only
          }
        });
      }
    });

    return {
      ...project,
      fullKeywordData,
      availableDates: Array.from(allDates).sort() // Sort chronologically (oldest first)
    };
  } catch (error) {
    console.error('Error fetching project with full data:', error);
    throw error;
  }
};
