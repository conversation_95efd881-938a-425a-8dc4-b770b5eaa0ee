/* App Layout */
.app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.app-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Controls Panel */
.controls-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Project Selector */
.project-selector {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.project-selector h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.search-input,
.project-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.search-input:focus,
.project-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.project-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
}

.project-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

/* Keyword Filter */
.keyword-filter {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.keyword-filter h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.keyword-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.keyword-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.keyword-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
}

.keyword-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

/* Date Controls */
.date-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.date-controls h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.timeline-description {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
  text-align: center;
}

.date-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.playback-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.nav-button,
.play-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: background-color 0.2s;
}

.nav-button:hover:not(:disabled),
.play-button:hover {
  background: #5a6fd8;
}

.nav-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
}

.speed-slider {
  flex: 1;
}

.speed-label {
  font-weight: bold;
  min-width: 30px;
}

.date-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
}

.date-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.date-range {
  color: #666;
  font-size: 0.8rem;
}

/* Timeline Indicator */
.timeline-indicator {
  margin-top: 1rem;
}

.timeline-bar {
  position: relative;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.timeline-progress {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.timeline-marker {
  position: absolute;
  top: -4px;
  width: 16px;
  height: 16px;
  background: white;
  border: 3px solid #667eea;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: left 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.timeline-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

/* Rank Display Controls */
.rank-display-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.rank-display-controls h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.rank-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.rank-toggle input[type="checkbox"] {
  margin: 0;
  transform: scale(1.2);
}

/* Map Container */
.map-container {
  margin: 2rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

/* Map Popup Styles */
.popup-content {
  min-width: 250px;
}

.popup-content h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.popup-content p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.ranking-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.ranking-info h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.keyword-ranking {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.85rem;
}

.best-position {
  color: #28a745;
  font-weight: normal;
}

/* Messages */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
  border: 1px solid #f5c6cb;
}

.info-message {
  background: #d1ecf1;
  color: #0c5460;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
  border: 1px solid #bee5eb;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 0.5rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .controls-panel {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .playback-controls {
    flex-wrap: wrap;
  }

  .speed-control {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Loading Animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 2s infinite;
}

/* Custom Rank Markers */
.custom-rank-marker {
  background: transparent !important;
  border: none !important;
}

.rank-marker {
  transition: all 0.2s ease;
  cursor: pointer;
}

.rank-marker:hover {
  transform: scale(1.1);
}

.rank-marker.selected {
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* Rank Legend */
.rank-legend {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.rank-legend h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #333;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Enhanced Popup */
.popup-content {
  min-width: 280px;
  max-width: 350px;
}

.popup-content h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.popup-content p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.ranking-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.ranking-info h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.keyword-ranking {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.85rem;
}

.best-position {
  color: #28a745;
  font-weight: normal;
}
