const KeywordFilter = ({ keywords, selectedKeyword, onKeywordSelect }) => {
  const handleKeywordChange = (e) => {
    onKeywordSelect(e.target.value);
  };

  return (
    <div className="keyword-filter">
      <h3>Filter by Keyword</h3>
      
      <select
        value={selectedKeyword}
        onChange={handleKeywordChange}
        className="keyword-select"
      >
        <option value="all">All Keywords</option>
        {keywords.map(keyword => (
          <option key={keyword.id} value={keyword.keyword}>
            {keyword.keyword} (Position: {keyword.current_position})
          </option>
        ))}
      </select>
      
      {selectedKeyword !== 'all' && (
        <div className="keyword-info">
          {(() => {
            const keyword = keywords.find(k => k.keyword === selectedKeyword);
            return keyword ? (
              <div>
                <p><strong>Current Position:</strong> {keyword.current_position}</p>
                <p><strong>Best Position:</strong> {keyword.best_position}</p>
                <p><strong>Last Checked:</strong> {new Date(keyword.last_checked).toLocaleString()}</p>
                <p><strong>Search Volume:</strong> {keyword.search_volume}</p>
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
};

export default KeywordFilter;
