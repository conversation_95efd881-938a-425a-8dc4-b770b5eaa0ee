import { useState } from 'react';

const ProjectSelector = ({ projects, selectedProject, onProjectSelect, loading }) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Filter projects based on search term
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.url.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProjectChange = (e) => {
    const projectId = parseInt(e.target.value);
    const project = projects.find(p => p.id === projectId);
    onProjectSelect(project || null);
  };

  return (
    <div className="project-selector">
      <h3>Select Project</h3>
      
      {loading ? (
        <div className="loading">Loading projects...</div>
      ) : (
        <>
          <div className="search-box">
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <select
            value={selectedProject?.id || ''}
            onChange={handleProjectChange}
            className="project-select"
          >
            <option value="">Choose a project...</option>
            {filteredProjects.map(project => (
              <option key={project.id} value={project.id}>
                {project.name} ({project.url})
              </option>
            ))}
          </select>
          
          {selectedProject && (
            <div className="project-info">
              <p><strong>Selected:</strong> {selectedProject.name}</p>
              <p><strong>URL:</strong> {selectedProject.url}</p>
              <p><strong>Keywords:</strong> {selectedProject.number_of_keywords}</p>
              <p><strong>Check Frequency:</strong> Every {selectedProject.check_frequency} hours</p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProjectSelector;
