import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { getProjects, getProject, getProjectWithFullData, getMultipleProjectsWithFullData } from './services/serprobotApi';
import { extractLocationFromProjectName, getUniqueLocations, DEFAULT_MAP_CENTER, DEFAULT_MAP_ZOOM } from './utils/locationExtractor';
import ProjectSelector from './components/ProjectSelector';
import DomainSelector from './components/DomainSelector';
import DateControls from './components/DateControls';
import KeywordFilter from './components/KeywordFilter';
import RankingMarker from './components/RankingMarker';
import './App.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

function App() {
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedProjects, setSelectedProjects] = useState([]);
  const [projectData, setProjectData] = useState(null);
  const [multiProjectData, setMultiProjectData] = useState([]);
  const [fullKeywordData, setFullKeywordData] = useState([]);
  const [selectedKeyword, setSelectedKeyword] = useState('all');
  const [selectedDate, setSelectedDate] = useState(null);
  const [availableDates, setAvailableDates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showBestRank, setShowBestRank] = useState(false);
  const [viewMode, setViewMode] = useState('single'); // 'single' or 'multi'

  // Load projects on component mount
  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true);
        const projectsData = await getProjects();
        setProjects(projectsData);
        setError(null);
      } catch (err) {
        setError('Failed to load projects');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, []);

  // Load project data when a project is selected
  useEffect(() => {
    const loadProjectData = async () => {
      if (!selectedProject) {
        setProjectData(null);
        setFullKeywordData([]);
        setAvailableDates([]);
        setSelectedDate(null);
        return;
      }

      try {
        setLoading(true);

        // Try to get enhanced data with historical information
        try {
          const enhancedData = await getProjectWithFullData(selectedProject.id);
          setProjectData(enhancedData);
          setFullKeywordData(enhancedData.fullKeywordData || []);
          setAvailableDates(enhancedData.availableDates || []);
          setSelectedDate(enhancedData.availableDates?.[0] || null); // Start with oldest date
        } catch (enhancedError) {
          console.warn('Failed to get enhanced data, falling back to basic data:', enhancedError);

          // Fallback to basic project data
          const basicData = await getProject(selectedProject.id);
          setProjectData(basicData);
          setFullKeywordData([]);

          // Extract dates from basic keyword data
          const dates = new Set();
          basicData.keywords.forEach(keyword => {
            if (keyword.last_checked) {
              dates.add(keyword.last_checked.split(' ')[0]);
            }
          });

          const sortedDates = Array.from(dates).sort(); // Sort chronologically (oldest first)
          setAvailableDates(sortedDates);
          setSelectedDate(sortedDates[0] || null); // Start with oldest date
        }

        setError(null);
      } catch (err) {
        setError('Failed to load project data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadProjectData();
  }, [selectedProject]);

  // Load multiple projects data when projects are selected
  useEffect(() => {
    const loadMultiProjectData = async () => {
      if (selectedProjects.length === 0) {
        setMultiProjectData([]);
        setAvailableDates([]);
        setSelectedDate(null);
        return;
      }

      try {
        setLoading(true);

        const projectIds = selectedProjects.map(p => p.id);
        const data = await getMultipleProjectsWithFullData(projectIds);

        setMultiProjectData(data.projects);
        setAvailableDates(data.availableDates);
        setSelectedDate(data.availableDates[0] || null);

        setError(null);
      } catch (err) {
        setError('Failed to load multiple projects data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (viewMode === 'multi') {
      loadMultiProjectData();
    }
  }, [selectedProjects, viewMode]);

  // Get location for the selected project
  const getProjectLocation = () => {
    if (!selectedProject) return null;
    return extractLocationFromProjectName(selectedProject.name);
  };

  // Get locations for multiple projects
  const getMultiProjectLocations = () => {
    if (selectedProjects.length === 0) return [];

    return selectedProjects.map(project => {
      const location = extractLocationFromProjectName(project.name);
      return location ? { ...location, project } : null;
    }).filter(Boolean);
  };

  // Get ranking data for display based on selected date
  const getRankingData = () => {
    if (!projectData) return [];

    const location = getProjectLocation();
    if (!location) return [];

    // If we have full keyword data with historical information, use it
    if (fullKeywordData.length > 0 && selectedDate) {
      return fullKeywordData
        .filter(keywordData => selectedKeyword === 'all' || keywordData.keyword === selectedKeyword)
        .map(keywordData => {
          // Find ranking for the selected date
          let positionForDate = keywordData.current_position;
          let bestPosition = keywordData.best_position;

          if (keywordData.check_data && Array.isArray(keywordData.check_data)) {
            const checkForDate = keywordData.check_data.find(check =>
              check.created && check.created.split(' ')[0] === selectedDate
            );
            if (checkForDate) {
              positionForDate = checkForDate.position;
            }
          }

          return {
            keyword: keywordData.keyword,
            position: positionForDate,
            bestPosition: bestPosition,
            location: location,
            lastChecked: keywordData.last_checked,
            dateSpecific: true
          };
        });
    }

    // Fallback to current data from basic project info
    return projectData.keywords
      .filter(keyword => selectedKeyword === 'all' || keyword.keyword === selectedKeyword)
      .map(keyword => ({
        keyword: keyword.keyword,
        position: keyword.current_position,
        bestPosition: keyword.best_position,
        location: location,
        lastChecked: keyword.last_checked,
        dateSpecific: false
      }));
  };

  // Get ranking data for multiple projects
  const getMultiProjectRankingData = () => {
    if (multiProjectData.length === 0) return [];

    const projectLocations = getMultiProjectLocations();
    const rankingsByLocation = [];

    projectLocations.forEach(locationData => {
      const project = locationData.project;
      const projectData = multiProjectData.find(p => p.id === project.id);

      if (projectData) {
        const rankings = projectData.keywords
          .filter(keyword => selectedKeyword === 'all' || keyword.keyword === selectedKeyword)
          .map(keyword => ({
            keyword: keyword.keyword,
            position: keyword.current_position,
            bestPosition: keyword.best_position,
            location: locationData,
            lastChecked: keyword.last_checked,
            projectName: project.name,
            projectId: project.id,
            dateSpecific: false
          }));

        if (rankings.length > 0) {
          rankingsByLocation.push({
            location: locationData,
            project: project,
            rankings: rankings
          });
        }
      }
    });

    return rankingsByLocation;
  };

  const rankingData = viewMode === 'single' ? getRankingData() : [];
  const multiRankingData = viewMode === 'multi' ? getMultiProjectRankingData() : [];
  const projectLocation = getProjectLocation();
  const multiProjectLocations = getMultiProjectLocations();

  return (
    <div className="app">
      <header className="app-header">
        <h1>SerpRobot Rankings Map</h1>
        <p>Visualize keyword rankings on an interactive map</p>
      </header>

      <div className="view-mode-toggle">
        <h3>View Mode</h3>
        <div className="toggle-buttons">
          <button
            className={`toggle-button ${viewMode === 'single' ? 'active' : ''}`}
            onClick={() => setViewMode('single')}
          >
            📍 Single Project
          </button>
          <button
            className={`toggle-button ${viewMode === 'multi' ? 'active' : ''}`}
            onClick={() => setViewMode('multi')}
          >
            🌐 Multiple Projects
          </button>
        </div>
      </div>

      <div className="controls-panel">
        {viewMode === 'single' ? (
          <ProjectSelector
            projects={projects}
            selectedProject={selectedProject}
            onProjectSelect={setSelectedProject}
            loading={loading}
          />
        ) : (
          <DomainSelector
            projects={projects}
            selectedProjects={selectedProjects}
            onProjectsSelect={setSelectedProjects}
            loading={loading}
          />
        )}

        {((viewMode === 'single' && projectData) || (viewMode === 'multi' && multiProjectData.length > 0)) && (
          <>
            <KeywordFilter
              keywords={viewMode === 'single' ? projectData.keywords :
                multiProjectData.flatMap(p => p.keywords).filter((keyword, index, self) =>
                  self.findIndex(k => k.keyword === keyword.keyword) === index
                )}
              selectedKeyword={selectedKeyword}
              onKeywordSelect={setSelectedKeyword}
            />

            <DateControls
              availableDates={availableDates}
              selectedDate={selectedDate}
              onDateSelect={setSelectedDate}
            />
          </>
        )}

        {((viewMode === 'single' && projectData) || (viewMode === 'multi' && multiProjectData.length > 0)) && selectedKeyword === 'all' && (
          <div className="rank-display-controls">
            <h3>Rank Display</h3>
            <label className="rank-toggle">
              <input
                type="checkbox"
                checked={showBestRank}
                onChange={(e) => setShowBestRank(e.target.checked)}
              />
              Show best rank instead of average
            </label>
          </div>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="map-container">
        <MapContainer
          center={
            viewMode === 'single'
              ? (projectLocation?.coordinates || DEFAULT_MAP_CENTER)
              : (multiProjectLocations.length > 0
                  ? multiProjectLocations[0].coordinates
                  : DEFAULT_MAP_CENTER)
          }
          zoom={
            viewMode === 'single'
              ? (projectLocation ? 10 : DEFAULT_MAP_ZOOM)
              : (multiProjectLocations.length > 0 ? 8 : DEFAULT_MAP_ZOOM)
          }
          style={{ height: '600px', width: '100%' }}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {/* Single project marker */}
          {viewMode === 'single' && projectLocation && rankingData.length > 0 && (
            <RankingMarker
              position={projectLocation.coordinates}
              rankingData={rankingData}
              projectName={selectedProject?.name}
              locationName={projectLocation.displayName}
              selectedKeyword={selectedKeyword}
              selectedDate={selectedDate}
              showBestRank={showBestRank}
            />
          )}

          {/* Multiple project markers */}
          {viewMode === 'multi' && multiRankingData.map((locationData, index) => (
            <RankingMarker
              key={`${locationData.project.id}-${index}`}
              position={locationData.location.coordinates}
              rankingData={locationData.rankings}
              projectName={locationData.project.name}
              locationName={locationData.location.displayName}
              selectedKeyword={selectedKeyword}
              selectedDate={selectedDate}
              showBestRank={showBestRank}
            />
          ))}
        </MapContainer>
      </div>

      {selectedProject && !projectLocation && (
        <div className="info-message">
          <p>Location information not available for this project. The project may not include location data in its name.</p>
        </div>
      )}
    </div>
  );
}

export default App;
