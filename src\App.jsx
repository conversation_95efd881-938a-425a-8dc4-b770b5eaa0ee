import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { getProjects, getProject } from './services/serprobotApi';
import { extractLocationFromProjectName, getUniqueLocations, DEFAULT_MAP_CENTER, DEFAULT_MAP_ZOOM } from './utils/locationExtractor';
import ProjectSelector from './components/ProjectSelector';
import DateControls from './components/DateControls';
import KeywordFilter from './components/KeywordFilter';
import './App.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

function App() {
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectData, setProjectData] = useState(null);
  const [keywordData, setKeywordData] = useState([]);
  const [selectedKeyword, setSelectedKeyword] = useState('all');
  const [selectedDate, setSelectedDate] = useState(null);
  const [availableDates, setAvailableDates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load projects on component mount
  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true);
        const projectsData = await getProjects();
        setProjects(projectsData);
        setError(null);
      } catch (err) {
        setError('Failed to load projects');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, []);

  // Load project data when a project is selected
  useEffect(() => {
    const loadProjectData = async () => {
      if (!selectedProject) {
        setProjectData(null);
        setKeywordData([]);
        setAvailableDates([]);
        setSelectedDate(null);
        return;
      }

      try {
        setLoading(true);
        const data = await getProject(selectedProject.id);
        setProjectData(data);

        // Extract available dates from keyword data
        const dates = new Set();
        data.keywords.forEach(keyword => {
          // For now, we'll use the last_checked date
          // In a full implementation, we'd fetch all keyword data
          if (keyword.last_checked) {
            dates.add(keyword.last_checked.split(' ')[0]); // Get date part only
          }
        });

        const sortedDates = Array.from(dates).sort().reverse();
        setAvailableDates(sortedDates);
        setSelectedDate(sortedDates[0] || null);

        setError(null);
      } catch (err) {
        setError('Failed to load project data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadProjectData();
  }, [selectedProject]);

  // Get location for the selected project
  const getProjectLocation = () => {
    if (!selectedProject) return null;
    return extractLocationFromProjectName(selectedProject.name);
  };

  // Get ranking data for display
  const getRankingData = () => {
    if (!projectData || !selectedDate) return [];

    const location = getProjectLocation();
    if (!location) return [];

    return projectData.keywords
      .filter(keyword => selectedKeyword === 'all' || keyword.keyword === selectedKeyword)
      .map(keyword => ({
        keyword: keyword.keyword,
        position: keyword.current_position,
        bestPosition: keyword.best_position,
        location: location,
        lastChecked: keyword.last_checked
      }));
  };

  const rankingData = getRankingData();
  const projectLocation = getProjectLocation();

  return (
    <div className="app">
      <header className="app-header">
        <h1>SerpRobot Rankings Map</h1>
        <p>Visualize keyword rankings on an interactive map</p>
      </header>

      <div className="controls-panel">
        <ProjectSelector
          projects={projects}
          selectedProject={selectedProject}
          onProjectSelect={setSelectedProject}
          loading={loading}
        />

        {projectData && (
          <>
            <KeywordFilter
              keywords={projectData.keywords}
              selectedKeyword={selectedKeyword}
              onKeywordSelect={setSelectedKeyword}
            />

            <DateControls
              availableDates={availableDates}
              selectedDate={selectedDate}
              onDateSelect={setSelectedDate}
            />
          </>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="map-container">
        <MapContainer
          center={projectLocation?.coordinates || DEFAULT_MAP_CENTER}
          zoom={projectLocation ? 10 : DEFAULT_MAP_ZOOM}
          style={{ height: '600px', width: '100%' }}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {projectLocation && (
            <Marker position={projectLocation.coordinates}>
              <Popup>
                <div className="popup-content">
                  <h3>{selectedProject?.name}</h3>
                  <p><strong>Location:</strong> {projectLocation.displayName}</p>
                  {rankingData.length > 0 && (
                    <div className="ranking-info">
                      <h4>Current Rankings:</h4>
                      {rankingData.map((data, index) => (
                        <div key={index} className="keyword-ranking">
                          <strong>{data.keyword}:</strong> Position {data.position}
                          {data.bestPosition && (
                            <span className="best-position"> (Best: {data.bestPosition})</span>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Popup>
            </Marker>
          )}
        </MapContainer>
      </div>

      {selectedProject && !projectLocation && (
        <div className="info-message">
          <p>Location information not available for this project. The project may not include location data in its name.</p>
        </div>
      )}
    </div>
  );
}

export default App;
