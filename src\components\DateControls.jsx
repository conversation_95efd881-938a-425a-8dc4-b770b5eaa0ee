import { useState, useEffect } from 'react';

const DateControls = ({ availableDates, selectedDate, onDateSelect }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [playSpeed, setPlaySpeed] = useState(1000); // milliseconds

  const handleDateChange = (e) => {
    onDateSelect(e.target.value);
  };

  const goToPreviousDate = () => {
    const currentIndex = availableDates.indexOf(selectedDate);
    if (currentIndex > 0) {
      onDateSelect(availableDates[currentIndex - 1]);
    }
  };

  const goToNextDate = () => {
    const currentIndex = availableDates.indexOf(selectedDate);
    if (currentIndex < availableDates.length - 1) {
      onDateSelect(availableDates[currentIndex + 1]);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  // Auto-play functionality
  useEffect(() => {
    let interval;
    if (isPlaying) {
      interval = setInterval(() => {
        const currentIndex = availableDates.indexOf(selectedDate);
        if (currentIndex < availableDates.length - 1) {
          onDateSelect(availableDates[currentIndex + 1]);
        } else {
          setIsPlaying(false); // Stop at the end
        }
      }, playSpeed);
    }
    return () => clearInterval(interval);
  }, [isPlaying, selectedDate, availableDates, playSpeed, onDateSelect]);

  if (availableDates.length === 0) {
    return null;
  }

  const currentIndex = availableDates.indexOf(selectedDate);
  const isFirstDate = currentIndex === 0;
  const isLastDate = currentIndex === availableDates.length - 1;

  return (
    <div className="date-controls">
      <h3>Date Navigation</h3>
      
      <div className="date-selector">
        <select
          value={selectedDate || ''}
          onChange={handleDateChange}
          className="date-select"
        >
          {availableDates.map(date => (
            <option key={date} value={date}>
              {new Date(date).toLocaleDateString()}
            </option>
          ))}
        </select>
      </div>
      
      <div className="playback-controls">
        <button
          onClick={goToPreviousDate}
          disabled={isFirstDate}
          className="nav-button"
          title="Previous Date"
        >
          ⏮️
        </button>
        
        <button
          onClick={togglePlayback}
          className="play-button"
          title={isPlaying ? "Pause" : "Play"}
        >
          {isPlaying ? '⏸️' : '▶️'}
        </button>
        
        <button
          onClick={goToNextDate}
          disabled={isLastDate}
          className="nav-button"
          title="Next Date"
        >
          ⏭️
        </button>
      </div>
      
      <div className="speed-control">
        <label htmlFor="speed-slider">Speed:</label>
        <input
          id="speed-slider"
          type="range"
          min="200"
          max="3000"
          step="200"
          value={playSpeed}
          onChange={(e) => setPlaySpeed(parseInt(e.target.value))}
          className="speed-slider"
        />
        <span className="speed-label">{(3000 - playSpeed) / 200 + 1}x</span>
      </div>
      
      <div className="date-info">
        <p>
          <strong>Selected Date:</strong> {selectedDate ? new Date(selectedDate).toLocaleDateString() : 'None'}
        </p>
        <p>
          <strong>Progress:</strong> {currentIndex + 1} of {availableDates.length}
        </p>
      </div>
    </div>
  );
};

export default DateControls;
