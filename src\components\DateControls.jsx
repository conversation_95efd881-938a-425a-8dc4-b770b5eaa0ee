import { useState, useEffect } from 'react';

const DateControls = ({ availableDates, selectedDate, onDateSelect }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [playSpeed, setPlaySpeed] = useState(1000); // milliseconds

  const handleDateChange = (e) => {
    onDateSelect(e.target.value);
  };

  const goToPreviousDate = () => {
    const currentIndex = availableDates.indexOf(selectedDate);
    if (currentIndex > 0) {
      onDateSelect(availableDates[currentIndex - 1]);
    }
  };

  const goToNextDate = () => {
    const currentIndex = availableDates.indexOf(selectedDate);
    if (currentIndex < availableDates.length - 1) {
      onDateSelect(availableDates[currentIndex + 1]);
    }
  };

  const goToFirstDate = () => {
    if (availableDates.length > 0) {
      onDateSelect(availableDates[0]);
    }
  };

  const goToLastDate = () => {
    if (availableDates.length > 0) {
      onDateSelect(availableDates[availableDates.length - 1]);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  // Auto-play functionality
  useEffect(() => {
    let interval;
    if (isPlaying) {
      interval = setInterval(() => {
        const currentIndex = availableDates.indexOf(selectedDate);
        if (currentIndex < availableDates.length - 1) {
          onDateSelect(availableDates[currentIndex + 1]);
        } else {
          setIsPlaying(false); // Stop at the end
        }
      }, playSpeed);
    }
    return () => clearInterval(interval);
  }, [isPlaying, selectedDate, availableDates, playSpeed, onDateSelect]);

  if (availableDates.length === 0) {
    return null;
  }

  const currentIndex = availableDates.indexOf(selectedDate);
  const isFirstDate = currentIndex === 0;
  const isLastDate = currentIndex === availableDates.length - 1;

  return (
    <div className="date-controls">
      <h3>Date Navigation</h3>
      <p className="timeline-description">
        📅 Navigate through time to see how rankings changed from past to present
      </p>
      
      <div className="date-selector">
        <select
          value={selectedDate || ''}
          onChange={handleDateChange}
          className="date-select"
        >
          {availableDates.map(date => (
            <option key={date} value={date}>
              {new Date(date).toLocaleDateString()}
            </option>
          ))}
        </select>
      </div>
      
      <div className="playback-controls">
        <button
          onClick={goToFirstDate}
          disabled={isFirstDate}
          className="nav-button"
          title="Go to First Date (Oldest)"
        >
          ⏪
        </button>

        <button
          onClick={goToPreviousDate}
          disabled={isFirstDate}
          className="nav-button"
          title="Previous Date (Older)"
        >
          ⏮️
        </button>

        <button
          onClick={togglePlayback}
          className="play-button"
          title={isPlaying ? "Pause Timeline" : "Play Timeline (Past → Present)"}
        >
          {isPlaying ? '⏸️' : '▶️'}
        </button>

        <button
          onClick={goToNextDate}
          disabled={isLastDate}
          className="nav-button"
          title="Next Date (Newer)"
        >
          ⏭️
        </button>

        <button
          onClick={goToLastDate}
          disabled={isLastDate}
          className="nav-button"
          title="Go to Last Date (Newest)"
        >
          ⏩
        </button>
      </div>
      
      <div className="speed-control">
        <label htmlFor="speed-slider">Speed:</label>
        <input
          id="speed-slider"
          type="range"
          min="200"
          max="3000"
          step="200"
          value={playSpeed}
          onChange={(e) => setPlaySpeed(parseInt(e.target.value))}
          className="speed-slider"
        />
        <span className="speed-label">{(3000 - playSpeed) / 200 + 1}x</span>
      </div>
      
      <div className="date-info">
        <p>
          <strong>Selected Date:</strong> {selectedDate ? new Date(selectedDate).toLocaleDateString() : 'None'}
        </p>
        <p>
          <strong>Timeline Progress:</strong> {currentIndex + 1} of {availableDates.length}
          {availableDates.length > 0 && (
            <span className="date-range">
              {' '}({new Date(availableDates[0]).toLocaleDateString()} → {new Date(availableDates[availableDates.length - 1]).toLocaleDateString()})
            </span>
          )}
        </p>
        {availableDates.length > 1 && (
          <div className="timeline-indicator">
            <div className="timeline-bar">
              <div
                className="timeline-progress"
                style={{ width: `${((currentIndex) / (availableDates.length - 1)) * 100}%` }}
              ></div>
              <div
                className="timeline-marker"
                style={{ left: `${((currentIndex) / (availableDates.length - 1)) * 100}%` }}
              ></div>
            </div>
            <div className="timeline-labels">
              <span>Past</span>
              <span>Present</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DateControls;
